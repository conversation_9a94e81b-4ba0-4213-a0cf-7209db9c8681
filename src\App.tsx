import React, { useState } from 'react';
import { Play } from 'lucide-react';
import Navigation from './components/Navigation';
import FeaturesSection from './components/FeaturesSection';
import SwipeSection from './components/SwipeSection';
import TryOnSection from './components/TryOnSection';

function App() {
  const [isPlaying, setIsPlaying] = useState(false);

  const handlePlayClick = () => {
    setIsPlaying(true);
    // Here you would typically start the video playback
    console.log('Video play clicked');
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <Navigation />
      
      {/* Hero Section */}
      <section className="px-6 py-16 md:py-24 lg:py-32 pt-32">
        <div className="max-w-6xl mx-auto">
          {/* Main Heading */}
          <h1 className="text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-serif text-center text-black mb-12 md:mb-16 lg:mb-20 leading-tight tracking-tight">
            Clothes that feel right
          </h1>
          
          {/* Video Container */}
          <div className="relative w-full max-w-5xl mx-auto">
            <div className="relative aspect-video bg-black rounded-2xl md:rounded-3xl overflow-hidden shadow-2xl">
              {/* Video Background with SwipeSense Branding */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center">
                  <h2 className="text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold">
                    <span className="bg-gradient-to-r from-purple-500 via-purple-600 to-red-500 bg-clip-text text-transparent">
                      Swipe
                    </span>
                    <span className="bg-gradient-to-r from-red-500 via-red-600 to-red-700 bg-clip-text text-transparent">
                      Sense
                    </span>
                  </h2>
                </div>
              </div>
              
              {/* Play Button Overlay */}
              {!isPlaying && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <button
                    onClick={handlePlayClick}
                    className="group relative w-16 h-16 md:w-20 md:h-20 lg:w-24 lg:h-24 bg-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 focus:outline-none focus:ring-4 focus:ring-white/30"
                    aria-label="Play video"
                  >
                    <Play className="w-6 h-6 md:w-8 md:h-8 lg:w-10 lg:h-10 text-black ml-1 group-hover:scale-110 transition-transform duration-200" fill="currentColor" />
                  </button>
                </div>
              )}
              
              {/* Actual Video Element (hidden initially) */}
              <video
                className={`w-full h-full object-cover transition-opacity duration-500 ${
                  isPlaying ? 'opacity-100' : 'opacity-0'
                }`}
                controls={isPlaying}
                poster="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 675'%3E%3Crect width='1200' height='675' fill='%23000'/%3E%3C/svg%3E"
              >
                {/* Replace with your actual video source */}
                <source src="/path-to-your-video.mp4" type="video/mp4" />
                Your browser does not support the video tag.
              </video>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <FeaturesSection />

      {/* Swipe Section */}
      <SwipeSection />

      {/* Try-On Section */}
      <TryOnSection />

      {/* Additional content for scroll testing */}
      <section className="px-6 py-32">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-serif mb-8">More sections coming soon</h2>
          <p className="text-lg text-gray-600 mb-16">
            Continue building your website section by section with beautiful, production-ready components.
          </p>
          <div className="h-96 bg-gradient-to-b from-gray-50 to-gray-100 rounded-3xl flex items-center justify-center">
            <p className="text-gray-500">Ready for your next section</p>
          </div>
        </div>
      </section>
    </div>
  );
}

export default App;