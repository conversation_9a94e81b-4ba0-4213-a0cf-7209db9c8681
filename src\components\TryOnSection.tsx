import React from 'react';

const TryOnSection = () => {
  return (
    <section className="px-6 py-24 lg:py-32 bg-white overflow-hidden">
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 lg:gap-24 items-center">
          {/* Left Content */}
          <div className="space-y-8">
            {/* Main Heading */}
            <h2 className="text-5xl md:text-6xl lg:text-7xl font-['DM_Serif_Text'] text-black leading-[1.1] tracking-tight">
              Try on virtually, 
              <br />
              shop confidently
            </h2>
            
            {/* Description */}
            <p className="text-lg md:text-xl text-gray-600 leading-relaxed max-w-lg font-['DM_Serif_Text'] italic">
              See how clothes look on you before you buy. Our AI-powered virtual try-on 
              technology helps you make confident purchase decisions from the comfort of your home.
            </p>
            
            {/* CTA <PERSON> */}
            <button className="bg-black text-white px-8 py-4 rounded-full font-medium text-lg hover:bg-gray-800 transition-colors duration-200 shadow-lg hover:shadow-xl">
              Try it now
            </button>
            
            {/* Features List */}
            <div className="space-y-4 pt-8">
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                <span className="text-gray-700 font-medium">Realistic virtual fitting</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                <span className="text-gray-700 font-medium">Size recommendation</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                <span className="text-gray-700 font-medium">Multiple angle views</span>
              </div>
            </div>
          </div>
          
          {/* Right Phone Mockup */}
          <div className="relative flex justify-center lg:justify-end">
            <div className="relative">
              {/* Phone Mockup Image */}
              <img 
                src="/assets/Mockup SS New-1 1.png"
                alt="SwipeSense App Virtual Try-On"
                className="w-full max-w-sm lg:max-w-md xl:max-w-lg h-auto object-contain"
              />
              
              {/* Floating Elements Around Phone */}
              <div className="absolute -top-8 -left-8 w-16 h-16 bg-gradient-to-br from-purple-400 to-pink-400 rounded-full opacity-20 animate-pulse"></div>
              <div className="absolute -bottom-12 -right-8 w-20 h-20 bg-gradient-to-br from-blue-400 to-purple-400 rounded-full opacity-15 animate-pulse delay-1000"></div>
              <div className="absolute top-1/2 -left-12 w-12 h-12 bg-gradient-to-br from-pink-400 to-red-400 rounded-full opacity-25 animate-pulse delay-500"></div>
            </div>
          </div>
        </div>
        
        {/* Bottom Stats/Features */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mt-24 pt-16 border-t border-gray-100">
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-black mb-2">98%</div>
            <div className="text-gray-600 text-sm">Accuracy Rate</div>
          </div>
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-black mb-2">2M+</div>
            <div className="text-gray-600 text-sm">Virtual Try-Ons</div>
          </div>
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-black mb-2">50%</div>
            <div className="text-gray-600 text-sm">Return Reduction</div>
          </div>
          <div className="text-center">
            <div className="text-3xl md:text-4xl font-bold text-black mb-2">24/7</div>
            <div className="text-gray-600 text-sm">Available</div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TryOnSection;