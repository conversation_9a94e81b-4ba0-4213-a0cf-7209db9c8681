import React, { useState, useEffect } from 'react';
import { User } from 'lucide-react';

const Navigation = () => {
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 px-6 py-4">
      <div className="max-w-7xl mx-auto flex items-center justify-between">
        {/* Logo - Completely Static (no transforms or animations) */}
        <div className="flex items-center">
          <img 
            src="/assets/Logo SS.png" 
            alt="SwipeSense Logo" 
            className="h-8 w-auto"
            onError={(e) => {
              // Fallback if image doesn't load
              const target = e.currentTarget as HTMLImageElement;
              target.style.display = 'none';
              const fallback = target.nextElementSibling as HTMLElement;
              if (fallback) fallback.style.display = 'block';
            }}
          />
          {/* Fallback text logo (hidden when image loads) */}
          <div className="text-2xl font-bold text-black" style={{ display: 'none' }}>
            SwipeSense
          </div>
        </div>

        {/* Center Navigation - Floating Glassy (only this moves) */}
        <div className={`
          absolute left-1/2 transform -translate-x-1/2 transition-all duration-500 ease-out
          ${isScrolled ? 'translate-y-2 scale-95' : 'translate-y-0 scale-100'}
        `}>
          <div className="
            backdrop-blur-md bg-white/10 border border-white/20 
            rounded-full px-8 py-3 shadow-lg shadow-black/5
            hover:bg-white/15 hover:border-white/30 transition-all duration-300
          ">
            <div className="flex items-center space-x-8">
              <a 
                href="#collections" 
                className="text-black/80 hover:text-black font-medium transition-colors duration-200 hover:scale-105 transform"
              >
                Collections
              </a>
              <a 
                href="#ai-features" 
                className="text-black/80 hover:text-black font-medium transition-colors duration-200 hover:scale-105 transform"
              >
                AI Features
              </a>
              <a 
                href="#features" 
                className="text-black/80 hover:text-black font-medium transition-colors duration-200 hover:scale-105 transform"
              >
                Features
              </a>
            </div>
          </div>
        </div>

        {/* Access Button - Completely Static (no transforms or animations on scroll) */}
        <div>
          <button className="
            flex items-center space-x-2 bg-black text-white px-6 py-3 rounded-full
            hover:bg-gray-800 transition-colors duration-200
            shadow-lg hover:shadow-xl
          ">
            <User className="w-4 h-4" />
            <span className="font-medium">Access</span>
          </button>
        </div>
      </div>
    </nav>
  );
};

export default Navigation;