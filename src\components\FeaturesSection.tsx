import React from 'react';

const FeaturesSection = () => {
  const features = [
    {
      number: "01",
      title: "From chill to chic, our clothes cover every vibe. Your closet's about to get jealous ;)",
      image: "https://images.pexels.com/photos/7691691/pexels-photo-7691691.jpeg?auto=compress&cs=tinysrgb&w=400&h=500&fit=crop"
    },
    {
      number: "02", 
      title: "Left, right, love at first swipe, we're decoding your style crushes in real time!",
      image: "https://images.pexels.com/photos/4050315/pexels-photo-4050315.jpeg?auto=compress&cs=tinysrgb&w=400&h=500&fit=crop"
    },
    {
      number: "03",
      title: "Craft your wardrobe, remix your style, and let the world double-tap your drip.",
      image: "https://images.pexels.com/photos/6069112/pexels-photo-6069112.jpeg?auto=compress&cs=tinysrgb&w=400&h=500&fit=crop"
    },
    {
      number: "04",
      title: "Swipe, get styled, and try on virtually your AI stylist's got your back (and your closet)!",
      image: "https://images.pexels.com/photos/7691691/pexels-photo-7691691.jpeg?auto=compress&cs=tinysrgb&w=400&h=500&fit=crop"
    }
  ];

  return (
    <section className="px-6 py-24 lg:py-32 bg-white">
      <div className="max-w-7xl mx-auto">
        {/* Main Heading */}
        <div className="mb-16 lg:mb-24">
          <h2 className="text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-serif text-black leading-[1.1] max-w-5xl tracking-tight">
            Dress the way which expresses who you are.
          </h2>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-6">
          {features.map((feature, index) => (
            <div key={index} className="space-y-4">
              {/* Number - Changed size to 48px (text-5xl) and font to DM Serif Text */}
              <div className="text-5xl font-['DM_Serif_Text'] text-gray-200 leading-none font-bold">
                {feature.number}
              </div>
              
              {/* Description - Smaller, regular weight, not italic */}
              <p className="font-['DM_Serif_Text'] text-gray-600 text-sm md:text-base leading-relaxed italic font-medium max-w-[280px]">
              {feature.title}
              </p>
              
              {/* Image - Taller aspect ratio to match Figma */}
              <div className="aspect-[3/4] rounded-2xl overflow-hidden bg-gray-100 mt-6">
                <img 
                  src={feature.image}
                  alt={`Feature ${feature.number}`}
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;