import React, { useState } from 'react';
import { Play } from 'lucide-react';

const SwipeSection = () => {
  const [isPlaying, setIsPlaying] = useState(false);

  const handlePlayClick = () => {
    setIsPlaying(true);
    // Here you would typically start the video playback
    console.log('Video play clicked');
  };

  return (
    <section className="px-6 py-16 md:py-24 lg:py-32 pt-32 bg-white relative overflow-hidden">
      <div className="max-w-7xl mx-auto">
        {/* Top Section - Title, Description, Button, Phone */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center mb-16">
          {/* Left Content */}
          <div className="space-y-8">
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-['DM_Serif_Text'] text-blue-600 leading-tight">
              Swipe Through Styles, Let Your Taste Do the Talking ✨
            </h2>
            
            <p className="text-lg text-gray-600 font-['DM_Serif_Text'] italic leading-relaxed max-w-lg">
              Swipe right if you vibe with it, left if you don't — every swipe helps us learn your style. The more you swipe, the better your feed gets. And when you find that perfect piece? Just swipe up to make it yours.
            </p>
            
            <button className="bg-black text-white px-8 py-4 rounded-full font-medium hover:bg-gray-800 transition-colors duration-200">
              Get early access
            </button>
          </div>

          {/* Right Side - Phone Mockup */}
          <div className="relative">
            <img 
              src="/assets/Mockup SS New-1 1.png" 
              alt="SwipeSense App Mockup" 
              className="w-full max-w-md mx-auto lg:max-w-lg"
            />
          </div>
        </div>

        {/* Bottom Section - Horizontal Grid with Two Columns */}
<div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-start">
  {/* Left Grid - Three Oval Images */}
  <div className="grid grid-cols-3 gap-8 justify-center">
    {/* First oval */}
    <div className="relative">
      <div className="w-[200px] h-[369px] rounded-full overflow-hidden bg-amber-100 border-4 border-white shadow-lg">
        <img 
          src="https://images.pexels.com/photos/1040945/pexels-photo-1040945.jpeg?auto=compress&cs=tinysrgb&w=400&h=500&fit=crop" 
          alt="Style 1" 
          className="w-full h-full object-cover"
        />
      </div>
    </div>

    {/* Second oval */}
    <div className="relative">
      <div className="w-[200px] h-[369px] rounded-full overflow-hidden bg-green-100 border-4 border-white shadow-lg">
        <img 
          src="https://images.pexels.com/photos/1040945/pexels-photo-1040945.jpeg?auto=compress&cs=tinysrgb&w=400&h=500&fit=crop" 
          alt="Style 2" 
          className="w-full h-full object-cover"
        />
      </div>
    </div>

    {/* Third oval */}
    <div className="relative">
      <div className="w-[200px] h-[369px] rounded-full overflow-hidden bg-blue-100 border-4 border-white shadow-lg">
        <img 
          src="https://images.pexels.com/photos/1040945/pexels-photo-1040945.jpeg?auto=compress&cs=tinysrgb&w=400&h=500&fit=crop" 
          alt="Style 3" 
          className="w-full h-full object-cover"
        />
      </div>
    </div>
  </div>

  {/* Right Grid - Your Style Elevated Section */}
  <div className="space-y-4">
    <h3 className="text-2xl lg:text-3xl font-['DM_Serif_Text'] text-black">
      Your Style, Elevated
    </h3>
    
    <p className="text-gray-600 font-['DM_Serif_Text'] italic">
      We make sure that we know your fashion preferences better than you 😊
    </p>
    
    {/* Action Icons - Adjusted to match design */}
    <div className="flex items-center space-x-4 pt-4">
      <div className="w-12 h-12 bg-red-500 rounded-lg flex items-center justify-center">
        <span className="text-white text-xl">❤️</span>
      </div>
      <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center">
        <span className="text-white text-xl">👎</span>
      </div>
      <div className="w-12 h-12 bg-gray-800 rounded-lg flex items-center justify-center">
        <span className="text-white text-xl">🛒</span>
      </div>
      <div className="w-12 h-12 bg-teal-500 rounded-lg flex items-center justify-center">
        <span className="text-white text-xl">🔖</span>
      </div>
    </div>
  </div>
</div>
      </div>
    </section>
  );
};

export default SwipeSection;