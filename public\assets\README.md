# Assets Folder

This folder is for storing your website assets like images, videos, fonts, etc.

## How to use assets:

1. **Images**: Place your images here (e.g., `logo.png`, `hero-image.jpg`)
   - Reference them in your code as `/assets/filename.ext`
   - Example: `<img src="/assets/logo.png" alt="Logo" />`

2. **Videos**: Place your video files here
   - Reference them as `/assets/video-name.mp4`
   - Example: `<source src="/assets/hero-video.mp4" type="video/mp4" />`

3. **Other assets**: Icons, fonts, documents, etc.

## Current structure:
```
public/
├── assets/
│   ├── README.md (this file)
│   ├── (place your logo here as logo.png)
│   ├── (place your hero video here)
│   └── (other images and assets)
└── vite.svg (default Vite icon)
```

## Notes:
- Files in the `public` folder are served directly by Vite
- Always use absolute paths starting with `/` when referencing these assets
- Supported image formats: PNG, JPG, JPEG, SVG, WebP
- Supported video formats: MP4, WebM, OGV